<?php
/**
 * Test Login Script
 * This script will test the login functionality for the test user
 */

require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';

// Start session
startSecureSession();

// Test user credentials
$username = 'testuser';
$password = 'testpass123';

echo "<h1>Testing Login for Test User</h1>";

// Attempt authentication
$user = authenticateUser($username, $password);

if ($user) {
    echo "<p style='color: green;'>✅ Authentication successful!</p>";
    echo "<p>User details:</p>";
    echo "<ul>";
    echo "<li>ID: " . $user['id'] . "</li>";
    echo "<li>Username: " . $user['username'] . "</li>";
    echo "<li>User Type: " . $user['user_type'] . "</li>";
    echo "<li>Is Unified: " . ($user['is_unified_user'] ? 'Yes' : 'No') . "</li>";
    echo "<li>Primary Role: " . ($user['primary_role'] ?? 'None') . "</li>";
    echo "</ul>";
    
    // Login the user
    loginUser($user);
    
    echo "<p style='color: blue;'>✅ User logged in successfully!</p>";
    echo "<p>Session variables:</p>";
    echo "<ul>";
    foreach ($_SESSION as $key => $value) {
        echo "<li>$key: " . (is_array($value) ? json_encode($value) : $value) . "</li>";
    }
    echo "</ul>";
    
    // Test UnifiedUser class
    require_once 'classes/UnifiedUser.php';
    $unifiedUser = new UnifiedUser($user['id']);
    $userRoles = $unifiedUser->getActiveRoles();
    $primaryRole = $unifiedUser->getPrimaryRole();
    
    echo "<p>UnifiedUser test:</p>";
    echo "<ul>";
    echo "<li>Active Roles: " . json_encode($userRoles) . "</li>";
    echo "<li>Primary Role: " . ($primaryRole ?? 'None') . "</li>";
    echo "<li>Has No Roles: " . (empty($userRoles) ? 'Yes' : 'No') . "</li>";
    echo "</ul>";
    
    echo "<p><a href='dashboard/'>Go to Dashboard</a></p>";
    
} else {
    echo "<p style='color: red;'>❌ Authentication failed!</p>";
    echo "<p>Please check:</p>";
    echo "<ul>";
    echo "<li>Username: $username</li>";
    echo "<li>Password: $password</li>";
    echo "<li>User exists in database</li>";
    echo "<li>User status is active</li>";
    echo "</ul>";
}

// Check if user exists in database
$db = Database::getInstance();
$userCheck = $db->fetch("SELECT * FROM users WHERE username = ?", [$username]);

if ($userCheck) {
    echo "<p style='color: blue;'>User found in database:</p>";
    echo "<ul>";
    foreach ($userCheck as $key => $value) {
        if ($key !== 'password' && $key !== 'password_hash') {
            echo "<li>$key: " . ($value ?? 'NULL') . "</li>";
        }
    }
    echo "</ul>";
} else {
    echo "<p style='color: red;'>❌ User not found in database!</p>";
}
?>
