<?php
/**
 * Test Admin Login
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h2>Testing Admin Login</h2>";

try {
    $pdo = new PDO('mysql:host=localhost;dbname=blood_donation_system', 'root', '');
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check if admin user exists
    $stmt = $pdo->prepare("SELECT * FROM users WHERE username = ?");
    $stmt->execute(['admin']);
    $admin = $stmt->fetch();
    
    if ($admin) {
        echo "<p style='color: green;'>✅ Admin user exists</p>";
        echo "<p><strong>Admin Details:</strong></p>";
        echo "<ul>";
        echo "<li>Username: " . htmlspecialchars($admin['username']) . "</li>";
        echo "<li>Password: " . htmlspecialchars($admin['password']) . "</li>";
        echo "<li>User Type: " . htmlspecialchars($admin['user_type']) . "</li>";
        echo "<li>Status: " . htmlspecialchars($admin['status']) . "</li>";
        echo "</ul>";
        
        // Test login with admin credentials
        echo "<h3>Testing Login:</h3>";
        
        // Simulate the login process
        if ($admin['password'] === 'admin123') {
            echo "<p style='color: green;'>✅ Admin login should work with username: 'admin' and password: 'admin123'</p>";
        } else {
            echo "<p style='color: red;'>❌ Password mismatch. Expected: 'admin123', Found: '" . htmlspecialchars($admin['password']) . "'</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Admin user not found</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?> 