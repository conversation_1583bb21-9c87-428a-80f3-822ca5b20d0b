-- Fix Chat Messages Table
-- Blood Donation Management System

USE blood_donation_system;

-- Create chat_messages table
CREATE TABLE IF NOT EXISTS chat_messages (
    id INT PRIMARY KEY AUTO_INCREMENT,
    sender_id INT NOT NULL,
    receiver_id INT NOT NULL,
    message TEXT NOT NULL,
    is_read B<PERSON><PERSON><PERSON>N DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (sender_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (receiver_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_sender (sender_id),
    INDEX idx_receiver (receiver_id),
    INDEX idx_read (is_read),
    INDEX idx_created (created_at)
);

-- Create messaging_permissions table if it doesn't exist
CREATE TABLE IF NOT EXISTS messaging_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    can_message_admins BOOLEAN DEFAULT TRUE,
    can_message_donors BOOLEAN DEFAULT FALSE,
    can_message_recipients BOOLEAN DEFAULT FALSE,
    can_message_all BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_permissions (user_id)
);

-- Insert default messaging permissions for existing users
INSERT IGNORE INTO messaging_permissions (user_id, can_message_admins, can_message_donors, can_message_recipients, can_message_all)
SELECT 
    id,
    TRUE as can_message_admins,
    CASE WHEN user_type = 'admin' THEN TRUE ELSE FALSE END as can_message_donors,
    CASE WHEN user_type = 'admin' THEN TRUE ELSE FALSE END as can_message_recipients,
    CASE WHEN user_type = 'admin' THEN TRUE ELSE FALSE END as can_message_all
FROM users 
WHERE status = 'active';

COMMIT;
