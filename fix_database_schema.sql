-- Fix Database Schema for Blood Donation Management System
-- Add missing columns to users table

USE blood_donation_system;

-- Add missing columns to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS is_online TINYINT(1) DEFAULT 0,
ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS last_seen TIMESTAMP NULL;

-- Create user_sessions table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP NULL,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_session_id (session_id),
    INDEX idx_user_id (user_id),
    INDEX idx_active (is_active)
);

-- Create notifications table if it doesn't exist
CREATE TABLE IF NOT EXISTS notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    target_roles JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_active (is_active),
    INDEX idx_type (type)
);

-- Create user_notifications table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    notification_id INT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    read_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (notification_id) REFERENCES notifications(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_notification (user_id, notification_id),
    INDEX idx_user_id (user_id),
    INDEX idx_read (is_read)
);

-- Update existing users to have proper default values
UPDATE users SET 
    is_online = 0,
    last_activity = NULL,
    last_seen = NULL
WHERE is_online IS NULL;

-- Create a test unified user with no roles
INSERT IGNORE INTO users (
    username, 
    email, 
    password, 
    user_type, 
    first_name, 
    last_name, 
    phone, 
    address, 
    status, 
    email_verified, 
    is_unified_user, 
    primary_role, 
    registration_source, 
    created_at
) VALUES (
    'testuser',
    '<EMAIL>',
    'testpass123',
    'unified',
    'Test',
    'User',
    '1234567890',
    '123 Test Street',
    'active',
    0,
    1,
    NULL,
    'unified',
    NOW()
);

COMMIT;
