<?php
/**
 * Debug Dashboard Script
 * This script will help debug what's happening in the dashboard
 */

require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'classes/UnifiedUser.php';

// Start session
startSecureSession();

echo "<h1>Dashboard Debug Information</h1>";

// Check if user is logged in
if (!isLoggedIn()) {
    echo "<p style='color: red;'>❌ User is not logged in!</p>";
    exit;
}

$currentUser = getCurrentUser();
echo "<h2>Current User</h2>";
echo "<pre>" . print_r($currentUser, true) . "</pre>";

// Load unified user
$unifiedUser = new UnifiedUser($currentUser['id']);

// Get user roles
$userRoles = $unifiedUser->getActiveRoles();
$primaryRole = $unifiedUser->getPrimaryRole();

echo "<h2>User Roles</h2>";
echo "<ul>";
echo "<li>Active Roles: " . json_encode($userRoles) . "</li>";
echo "<li>Primary Role: " . ($primaryRole ?? 'NULL') . "</li>";
echo "<li>Has No Roles: " . (empty($userRoles) ? 'YES' : 'NO') . "</li>";
echo "</ul>";

// Handle users with no roles - show welcome screen instead of redirecting
$hasNoRoles = empty($userRoles);

echo "<h2>Dashboard Logic</h2>";
echo "<ul>";
echo "<li>Has No Roles: " . ($hasNoRoles ? 'YES' : 'NO') . "</li>";
echo "</ul>";

// Get current active role from session or use primary role
$currentRole = null;
if (!$hasNoRoles) {
    $currentRole = $_SESSION['current_role'] ?? $primaryRole;

    // Validate current role
    if (!in_array($currentRole, $userRoles)) {
        $currentRole = $userRoles[0]; // Default to first available role
        $_SESSION['current_role'] = $currentRole;
    }
}

echo "<h2>Current Role</h2>";
echo "<ul>";
echo "<li>Current Role: " . ($currentRole ?? 'NULL') . "</li>";
echo "<li>Session Current Role: " . ($_SESSION['current_role'] ?? 'NULL') . "</li>";
echo "</ul>";

echo "<h2>Expected Dashboard Content</h2>";
if ($hasNoRoles) {
    echo "<p style='color: blue;'>✅ User should see role selection screen</p>";
    echo "<p>The dashboard should display:</p>";
    echo "<ul>";
    echo "<li>Welcome message for users with no roles</li>";
    echo "<li>Role selection options (Become a Donor, Become a Recipient)</li>";
    echo "<li>Basic navigation (Dashboard, Profile, Messages)</li>";
    echo "</ul>";
} else {
    echo "<p style='color: green;'>✅ User should see role-specific dashboard</p>";
    echo "<p>Current role: " . $currentRole . "</p>";
}

echo "<h2>Database Check</h2>";
$db = Database::getInstance();

// Check user_roles table
$roles = $db->fetchAll("SELECT * FROM user_roles WHERE user_id = ?", [$currentUser['id']]);
echo "<h3>User Roles in Database:</h3>";
if (empty($roles)) {
    echo "<p style='color: orange;'>No roles found in database</p>";
} else {
    echo "<pre>" . print_r($roles, true) . "</pre>";
}

// Check if donor/recipient profiles exist
$donorProfile = $db->fetch("SELECT COUNT(*) as count FROM donor_profiles WHERE user_id = ?", [$currentUser['id']]);
$recipientProfile = $db->fetch("SELECT COUNT(*) as count FROM recipient_profiles WHERE user_id = ?", [$currentUser['id']]);

echo "<h3>Profile Tables:</h3>";
echo "<ul>";
echo "<li>Donor Profile Exists: " . ($donorProfile['count'] > 0 ? 'YES' : 'NO') . "</li>";
echo "<li>Recipient Profile Exists: " . ($recipientProfile['count'] > 0 ? 'YES' : 'NO') . "</li>";
echo "</ul>";

echo "<p><a href='dashboard/'>Go to Actual Dashboard</a></p>";
?>
