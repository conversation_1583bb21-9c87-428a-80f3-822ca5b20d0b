<?php
/**
 * Test Dashboard Content
 * This script will test what content should be displayed
 */

require_once 'config/constants.php';
require_once 'config/database.php';
require_once 'includes/auth.php';
require_once 'includes/functions.php';
require_once 'classes/UnifiedUser.php';

// Start session
startSecureSession();

if (!isLoggedIn()) {
    echo "Please login first";
    exit;
}

$currentUser = getCurrentUser();
$unifiedUser = new UnifiedUser($currentUser['id']);
$userRoles = $unifiedUser->getActiveRoles();
$hasNoRoles = empty($userRoles);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard Content</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-4">
        <h1>Test Dashboard Content</h1>
        
        <div class="alert alert-info">
            <strong>Debug Info:</strong><br>
            User: <?php echo $currentUser['first_name']; ?><br>
            Has No Roles: <?php echo $hasNoRoles ? 'YES' : 'NO'; ?><br>
            User Roles: <?php echo json_encode($userRoles); ?>
        </div>

        <!-- Welcome Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card" style="background: linear-gradient(135deg, #8B0000 0%, #DC143C 100%);">
                    <div class="card-body text-white">
                        <h2>
                            <i class="fas fa-heart"></i> 
                            Welcome back, <?php echo htmlspecialchars($currentUser['first_name']); ?>!
                        </h2>
                        <p>
                            <?php if ($hasNoRoles): ?>
                                Welcome to our blood donation community! Please select your role below to get started.
                            <?php else: ?>
                                Welcome to your dashboard!
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Role Selection Section -->
        <?php if ($hasNoRoles): ?>
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header text-center">
                        <h3><i class="fas fa-star text-warning"></i> Choose Your Role</h3>
                        <p class="text-muted">Select how you'd like to participate in our blood donation community</p>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-heart fa-4x text-danger mb-3"></i>
                                        <h4>Blood Donor</h4>
                                        <p>Join thousands of heroes who save lives by donating blood.</p>
                                        <a href="add-role.php?role=donor" class="btn btn-danger">
                                            <i class="fas fa-heart me-2"></i>Become a Donor
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body text-center">
                                        <i class="fas fa-hand-holding-medical fa-4x text-primary mb-3"></i>
                                        <h4>Blood Recipient</h4>
                                        <p>Get the support you need when facing medical challenges.</p>
                                        <a href="add-role.php?role=recipient" class="btn btn-primary">
                                            <i class="fas fa-hand-holding-medical me-2"></i>Become a Recipient
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <?php else: ?>
        <div class="alert alert-success">
            <h4>You have roles assigned!</h4>
            <p>Your roles: <?php echo implode(', ', $userRoles); ?></p>
        </div>
        <?php endif; ?>

        <p><a href="dashboard/" class="btn btn-secondary">Go to Real Dashboard</a></p>
    </div>
</body>
</html>
