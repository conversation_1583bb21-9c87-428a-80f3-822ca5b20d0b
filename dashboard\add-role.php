<?php
/**
 * Add Role Page
 * Blood Donation Management System
 * 
 * Allows users to add additional roles to their account
 */

require_once '../config/constants.php';
require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/validation.php';
require_once '../classes/UnifiedUser.php';

// Start session and check authentication
startSecureSession();
requireLogin('../login.php');

$db = Database::getInstance();
$currentUser = getCurrentUser();
$unifiedUser = new UnifiedUser($currentUser['id']);

// Get requested role
$requestedRole = $_GET['role'] ?? '';
if (!in_array($requestedRole, ['donor', 'recipient'])) {
    redirectWithMessage('index.php', 'Invalid role specified.', 'error');
}

// Check if user already has this role
if ($unifiedUser->hasRole($requestedRole)) {
    redirectWithMessage('index.php', 'You already have this role.', 'info');
}

$errors = [];
$success = '';

// Get blood types for donor role
$bloodTypes = [];
if ($requestedRole === 'donor') {
    $bloodTypes = $db->fetchAll("SELECT * FROM blood_types ORDER BY type");
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $errors[] = 'Invalid security token. Please try again.';
    } else {
        try {
            // Add the role
            $unifiedUser->addRole($requestedRole);
            
            // Update role-specific profile data if provided
            if ($requestedRole === 'donor' && !empty($_POST['blood_type_id'])) {
                $db->execute("UPDATE donor_profiles SET blood_type_id = ?, weight = ?, birth_date = ?, medical_conditions = ? WHERE user_id = ?", [
                    (int)$_POST['blood_type_id'],
                    (float)($_POST['weight'] ?? 0),
                    $_POST['birth_date'] ?? null,
                    sanitizeInput($_POST['medical_conditions'] ?? ''),
                    $currentUser['id']
                ]);
            } elseif ($requestedRole === 'recipient') {
                $db->execute("UPDATE recipient_profiles SET medical_condition = ?, emergency_contact = ?, emergency_phone = ?, doctor_name = ?, doctor_contact = ? WHERE user_id = ?", [
                    sanitizeInput($_POST['medical_condition'] ?? ''),
                    sanitizeInput($_POST['emergency_contact'] ?? ''),
                    sanitizeInput($_POST['emergency_phone'] ?? ''),
                    sanitizeInput($_POST['doctor_name'] ?? ''),
                    sanitizeInput($_POST['doctor_contact'] ?? ''),
                    $currentUser['id']
                ]);
            }
            
            redirectWithMessage('index.php', 'Role added successfully! You can now switch to ' . ucfirst($requestedRole) . ' mode.', 'success');
            
        } catch (Exception $e) {
            $errors[] = $e->getMessage();
        }
    }
}

$csrfToken = generateCSRFToken();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add <?php echo ucfirst($requestedRole); ?> Role - <?php echo APP_NAME; ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(90deg, #8B0000 0%, #DC143C 100%); box-shadow: 0 2px 10px rgba(139,0,0,0.3);">
        <div class="container-fluid">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-heart"></i> <?php echo APP_NAME; ?>
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="index.php">
                    <i class="fas fa-arrow-left"></i> Back to Dashboard
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header blood-theme text-white">
                        <h4>
                            <i class="fas fa-<?php echo $requestedRole === 'donor' ? 'heart' : 'hand-holding-medical'; ?>"></i>
                            Add <?php echo ucfirst($requestedRole); ?> Role
                        </h4>
                        <p class="mb-0">Complete your <?php echo $requestedRole; ?> profile to get started</p>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo htmlspecialchars($error); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="" id="roleApplicationForm">
                            <input type="hidden" name="csrf_token" value="<?php echo $csrfToken; ?>">

                            <?php if ($requestedRole === 'donor'): ?>
                                <!-- Donor-specific fields -->
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Donor Requirements</h6>
                                    <ul class="mb-0">
                                        <li>Must be between 18-65 years old</li>
                                        <li>Minimum weight of 50 kg</li>
                                        <li>Good general health</li>
                                        <li>No recent illnesses or medications that prevent donation</li>
                                    </ul>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="blood_type_id" class="form-label">Blood Type *</label>
                                            <select class="form-select" id="blood_type_id" name="blood_type_id" required>
                                                <option value="">Select Blood Type</option>
                                                <?php foreach ($bloodTypes as $bloodType): ?>
                                                    <option value="<?php echo $bloodType['id']; ?>">
                                                        <?php echo htmlspecialchars($bloodType['type']); ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="birth_date" class="form-label">Birth Date</label>
                                            <input type="date" class="form-control" id="birth_date" name="birth_date">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="weight" class="form-label">Weight (kg)</label>
                                            <input type="number" class="form-control" id="weight" name="weight" 
                                                   min="50" max="200" step="0.1">
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="medical_conditions" class="form-label">Medical Conditions</label>
                                    <textarea class="form-control" id="medical_conditions" name="medical_conditions" 
                                              rows="3" placeholder="List any medical conditions, medications, or health issues..."></textarea>
                                    <div class="form-text">This information helps ensure safe blood donation.</div>
                                </div>

                            <?php elseif ($requestedRole === 'recipient'): ?>
                                <!-- Recipient-specific fields -->
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle"></i> Recipient Information</h6>
                                    <p class="mb-0">Provide emergency contact and medical information to help us assist you better during blood requests.</p>
                                </div>

                                <div class="mb-3">
                                    <label for="medical_condition" class="form-label">Medical Condition</label>
                                    <textarea class="form-control" id="medical_condition" name="medical_condition" 
                                              rows="3" placeholder="Describe your medical condition or reason for needing blood..."></textarea>
                                </div>

                                <h6><i class="fas fa-phone"></i> Emergency Contact Information</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emergency_contact" class="form-label">Emergency Contact Name</label>
                                            <input type="text" class="form-control" id="emergency_contact" name="emergency_contact">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="emergency_phone" class="form-label">Emergency Contact Phone</label>
                                            <input type="tel" class="form-control" id="emergency_phone" name="emergency_phone">
                                        </div>
                                    </div>
                                </div>

                                <h6><i class="fas fa-user-md"></i> Doctor Information</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="doctor_name" class="form-label">Doctor Name</label>
                                            <input type="text" class="form-control" id="doctor_name" name="doctor_name">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="doctor_contact" class="form-label">Doctor Contact</label>
                                            <input type="tel" class="form-control" id="doctor_contact" name="doctor_contact">
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="index.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-danger" id="submitBtn">
                                    <span class="btn-text">
                                        <i class="fas fa-plus"></i> Add <?php echo ucfirst($requestedRole); ?> Role
                                    </span>
                                    <span class="btn-loading d-none">
                                        <i class="fas fa-spinner fa-spin"></i> Processing...
                                    </span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.getElementById('roleApplicationForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const form = this;
            const submitBtn = document.getElementById('submitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');

            // Show loading state
            btnText.classList.add('d-none');
            btnLoading.classList.remove('d-none');
            submitBtn.disabled = true;

            // Clear previous errors
            const existingAlert = document.querySelector('.alert-danger');
            if (existingAlert) {
                existingAlert.remove();
            }

            // Prepare form data
            const formData = new FormData(form);

            // Submit via AJAX
            fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                // Parse response to check for errors
                const parser = new DOMParser();
                const doc = parser.parseFromString(html, 'text/html');
                const errorAlert = doc.querySelector('.alert-danger');

                if (errorAlert) {
                    // Show errors
                    const cardBody = document.querySelector('.card-body');
                    cardBody.insertBefore(errorAlert, cardBody.firstChild);

                    // Reset button state
                    btnText.classList.remove('d-none');
                    btnLoading.classList.add('d-none');
                    submitBtn.disabled = false;
                } else {
                    // Success - redirect to dashboard with success message
                    window.location.href = 'index.php?success=role_added&role=<?php echo $requestedRole; ?>';
                }
            })
            .catch(error => {
                console.error('Error:', error);

                // Show generic error
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger';
                errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> An error occurred. Please try again.';

                const cardBody = document.querySelector('.card-body');
                cardBody.insertBefore(errorDiv, cardBody.firstChild);

                // Reset button state
                btnText.classList.remove('d-none');
                btnLoading.classList.add('d-none');
                submitBtn.disabled = false;
            });
        });
    </script>
</body>
</html>
